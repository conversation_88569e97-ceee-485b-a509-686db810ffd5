/**
 * 登录管理器
 * 负责处理用户登录流程、权限获取、服务器选择等功能
 */

class LoginManager {
  constructor(game) {
    this.game = game;
    this.isLoggedIn = false;
    this.userOpenId = null;
    this.selectedServer = null;
    this.hasPrivacyAuth = false;
    
    // 登录状态常量
    this.LOGIN_STATUS = {
      NOT_LOGGED: 'not_logged',      // 未登录
      PRIVACY_PENDING: 'privacy_pending', // 等待隐私授权
      SERVER_SELECTION: 'server_selection', // 服务器选择
      LOGGED_IN: 'logged_in'         // 已登录
    };
    
    this.currentStatus = this.LOGIN_STATUS.NOT_LOGGED;
  }

  /**
   * 初始化登录流程
   * 游戏加载完成后调用此方法
   */
  async initializeLogin() {
    console.log('开始初始化登录流程...');
    
    try {
      // 1. 检查用户是否曾经登录过
      const hasLoggedBefore = await this.checkPreviousLogin();
      
      if (hasLoggedBefore) {
        console.log('检测到用户曾经登录过，开始自动登录流程');
        await this.autoLogin();
      } else {
        console.log('用户首次使用，需要进行完整登录流程');
        this.currentStatus = this.LOGIN_STATUS.PRIVACY_PENDING;
        this.showPrivacyAuthDialog();
      }
    } catch (error) {
      console.error('初始化登录流程失败:', error);
      this.handleLoginError(error);
    }
  }

  /**
   * 检查用户是否曾经登录过
   */
  async checkPreviousLogin() {
    try {
      // 检查本地存储中的授权信息
      const userInfo = wx.getStorageSync('userInfo');
      const lastServer = wx.getStorageSync('lastSelectedServer');
      
      if (userInfo && userInfo.authorized && lastServer) {
        console.log('发现本地授权信息和服务器记录');
        return true;
      }

      // 检查微信授权设置
      const authSetting = await this.getAuthSetting();
      if (authSetting && authSetting['scope.userInfo'] === true) {
        console.log('发现微信授权记录');
        return true;
      }

      return false;
    } catch (error) {
      console.error('检查登录历史失败:', error);
      return false;
    }
  }

  /**
   * 获取微信授权设置
   */
  getAuthSetting() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => resolve(res.authSetting),
        fail: (err) => reject(err)
      });
    });
  }

  /**
   * 自动登录流程（用户曾经登录过）
   */
  async autoLogin() {
    try {
      console.log('开始自动登录...');
      
      // 1. 获取用户openid
      const openid = await this.getUserOpenId();
      if (!openid) {
        throw new Error('获取openid失败');
      }
      
      this.userOpenId = openid;
      console.log('获取openid成功:', openid);

      // 2. 获取用户最后登录的服务器
      const lastServer = wx.getStorageSync('lastSelectedServer');
      if (lastServer) {
        this.selectedServer = lastServer;
        console.log('使用上次选择的服务器:', lastServer);
      } else {
        // 如果没有服务器记录，使用默认服务器
        this.selectedServer = this.getDefaultServer();
        console.log('使用默认服务器:', this.selectedServer);
      }

      // 3. 从云数据库加载用户数据
      await this.loadUserDataFromCloud();

      // 4. 启动与openid相关的功能
      this.startOpenIdRelatedFeatures();

      // 5. 标记登录完成
      this.completeLogin();

    } catch (error) {
      console.error('自动登录失败:', error);
      // 自动登录失败，回退到完整登录流程
      this.currentStatus = this.LOGIN_STATUS.PRIVACY_PENDING;
      this.showPrivacyAuthDialog();
    }
  }

  /**
   * 显示隐私权限授权弹窗
   */
  showPrivacyAuthDialog() {
    console.log('显示隐私权限授权弹窗');
    
    wx.showModal({
      title: '隐私权限授权',
      content: '为了保存您的游戏进度和提供更好的游戏体验，需要获取您的基本信息权限。',
      confirmText: '同意授权',
      cancelText: '暂不授权',
      success: (res) => {
        if (res.confirm) {
          this.requestPrivacyAuth();
        } else {
          this.handlePrivacyAuthDenied();
        }
      }
    });
  }

  /**
   * 请求隐私权限授权
   */
  requestPrivacyAuth() {
    console.log('请求隐私权限授权...');
    
    wx.getUserProfile({
      desc: '用于保存游戏进度和个性化体验',
      lang: 'zh_CN',
      success: (res) => {
        console.log('隐私权限授权成功:', res.userInfo);
        this.hasPrivacyAuth = true;
        
        // 保存用户信息
        this.saveUserInfo(res.userInfo);
        
        // 进入服务器选择阶段
        this.currentStatus = this.LOGIN_STATUS.SERVER_SELECTION;
        this.showServerSelection();
      },
      fail: (err) => {
        console.error('隐私权限授权失败:', err);
        this.handlePrivacyAuthDenied();
      }
    });
  }

  /**
   * 处理隐私权限授权被拒绝
   */
  handlePrivacyAuthDenied() {
    console.log('用户拒绝隐私权限授权');
    
    wx.showModal({
      title: '提示',
      content: '未授权将无法保存游戏进度，您可以继续游戏但数据不会同步到云端。是否重新授权？',
      confirmText: '重新授权',
      cancelText: '继续游戏',
      success: (res) => {
        if (res.confirm) {
          this.showPrivacyAuthDialog();
        } else {
          // 用户选择继续游戏，使用离线模式
          this.startOfflineMode();
        }
      }
    });
  }

  /**
   * 显示服务器选择界面
   */
  showServerSelection() {
    console.log('显示服务器选择界面');
    
    const servers = this.getAvailableServers();
    const serverNames = servers.map(server => server.name);
    
    wx.showActionSheet({
      itemList: serverNames,
      success: (res) => {
        const selectedServer = servers[res.tapIndex];
        this.selectedServer = selectedServer;
        console.log('用户选择服务器:', selectedServer);
        
        // 保存服务器选择
        wx.setStorageSync('lastSelectedServer', selectedServer);
        
        // 进入游戏
        this.enterGame();
      },
      fail: () => {
        // 用户取消选择，使用默认服务器
        this.selectedServer = this.getDefaultServer();
        console.log('用户取消选择，使用默认服务器:', this.selectedServer);
        this.enterGame();
      }
    });
  }

  /**
   * 进入游戏（完成服务器选择后）
   */
  async enterGame() {
    try {
      console.log('开始进入游戏...');
      
      // 1. 获取用户openid
      const openid = await this.getUserOpenId();
      if (!openid) {
        throw new Error('获取openid失败');
      }
      
      this.userOpenId = openid;
      console.log('获取openid成功:', openid);

      // 2. 从云数据库加载用户数据
      await this.loadUserDataFromCloud();

      // 3. 启动与openid相关的功能
      this.startOpenIdRelatedFeatures();

      // 4. 标记登录完成
      this.completeLogin();

    } catch (error) {
      console.error('进入游戏失败:', error);
      this.handleLoginError(error);
    }
  }

  /**
   * 获取用户openid
   */
  getUserOpenId() {
    return new Promise((resolve, reject) => {
      if (!this.game.cloud) {
        reject(new Error('云环境未初始化'));
        return;
      }

      this.game.cloud.callFunction({
        name: 'login',
        success: (res) => {
          const openid = res.result.openid;
          if (openid) {
            resolve(openid);
          } else {
            reject(new Error('openid为空'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }

  /**
   * 从云数据库加载用户数据
   */
  async loadUserDataFromCloud() {
    if (!this.userOpenId) {
      console.warn('没有openid，跳过云数据库加载');
      return;
    }

    try {
      console.log('从云数据库加载用户数据...');
      
      // 使用新的DatabaseManager加载数据
      if (this.game.gameStateManager && this.game.gameStateManager.databaseManager) {
        const success = await this.game.gameStateManager.loadGameStateFromCloud();
        if (success) {
          console.log('从云数据库加载用户数据成功');
        } else {
          console.log('云数据库加载失败，使用本地数据');
        }
      }
    } catch (error) {
      console.error('从云数据库加载用户数据失败:', error);
    }
  }

  /**
   * 启动与openid相关的功能
   */
  startOpenIdRelatedFeatures() {
    console.log('启动与openid相关的功能...');
    
    // 设置全局openid
    if (typeof window !== 'undefined') {
      window.userOpenId = this.userOpenId;
    }
    wx.setStorageSync('openid', this.userOpenId);

    // 启动数据库相关功能
    if (this.game.gameStateManager) {
      // 标记游戏状态管理器可以使用数据库功能
      this.game.gameStateManager.gameInitialized = true;
    }

    // 启动自动同步
    if (this.game.startAutoSync) {
      this.game.startAutoSync();
    }

    console.log('openid相关功能启动完成');
  }

  /**
   * 完成登录
   */
  completeLogin() {
    this.isLoggedIn = true;
    this.currentStatus = this.LOGIN_STATUS.LOGGED_IN;
    
    console.log('登录流程完成');
    
    // 触发登录完成事件
    if (this.game.eventSystem) {
      this.game.eventSystem.emit('loginCompleted', {
        openid: this.userOpenId,
        server: this.selectedServer
      });
    }

    // 显示登录成功提示
    wx.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 2000
    });
  }

  /**
   * 启动离线模式
   */
  startOfflineMode() {
    console.log('启动离线模式');
    this.currentStatus = this.LOGIN_STATUS.NOT_LOGGED;
    
    wx.showToast({
      title: '离线模式',
      icon: 'none',
      duration: 2000
    });
  }

  /**
   * 处理登录错误
   */
  handleLoginError(error) {
    console.error('登录错误:', error);
    
    wx.showModal({
      title: '登录失败',
      content: '登录过程中出现错误，是否重试？',
      confirmText: '重试',
      cancelText: '离线游戏',
      success: (res) => {
        if (res.confirm) {
          this.initializeLogin();
        } else {
          this.startOfflineMode();
        }
      }
    });
  }

  /**
   * 保存用户信息
   */
  saveUserInfo(userInfo) {
    const userData = {
      ...userInfo,
      authorized: true,
      authorizeTime: Date.now()
    };
    
    wx.setStorageSync('userInfo', userData);
    
    // 同步到游戏状态管理器
    if (this.game.gameStateManager) {
      const player = this.game.gameStateManager.getPlayer();
      if (player) {
        player.nickname = userInfo.nickName || '修仙者';
        player.avatarUrl = userInfo.avatarUrl || '';
        this.game.gameStateManager.setPlayer(player);
      }
    }
  }

  /**
   * 获取可用服务器列表
   */
  getAvailableServers() {
    return [
      { id: 'server1', name: '青云门', region: 'cn-east' },
      { id: 'server2', name: '天音寺', region: 'cn-south' },
      { id: 'server3', name: '鬼王宗', region: 'cn-north' },
      { id: 'server4', name: '合欢派', region: 'cn-west' }
    ];
  }

  /**
   * 获取默认服务器
   */
  getDefaultServer() {
    return { id: 'server1', name: '青云门', region: 'cn-east' };
  }

  /**
   * 获取当前登录状态
   */
  getLoginStatus() {
    return {
      isLoggedIn: this.isLoggedIn,
      status: this.currentStatus,
      openid: this.userOpenId,
      server: this.selectedServer,
      hasPrivacyAuth: this.hasPrivacyAuth
    };
  }

  /**
   * 手动登录（用户点击登录按钮）
   */
  async manualLogin() {
    if (this.isLoggedIn) {
      console.log('用户已登录');
      return;
    }

    console.log('开始手动登录流程');
    await this.initializeLogin();
  }

  /**
   * 登出
   */
  logout() {
    this.isLoggedIn = false;
    this.userOpenId = null;
    this.selectedServer = null;
    this.hasPrivacyAuth = false;
    this.currentStatus = this.LOGIN_STATUS.NOT_LOGGED;
    
    // 清除本地存储
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('openid');
    wx.removeStorageSync('lastSelectedServer');
    
    console.log('用户已登出');
  }
}

export default LoginManager;
