/**
 * 角色模型类
 */
import AppContext from '../utils/AppContext';
import {
    generateCharacterAttributes
} from './CharacterTemplate.js';

class Character {
  constructor({
    id,
    name,
    level = 1,
    exp = 0,
    power = 0,
    attributes = {},
    equipments = [null, null, null, null],
    equipment = null, // 添加新的装备对象结构支持
    skills = [],
    equippedSkill = null,
    cultivation = '炼气期一层',
    lingli = 0,
    star = 0,
    hp,
    attack,
    defense,
    speed,
    critRate,
    critDamage,
    daoRule = 0,
    penetration = 0
  }) {
    this.id = id;
    this.name = name;
    this.level = level;
    // 统一使用exp作为修炼进度属性，lingli只作为兼容属性
    this.exp = lingli || exp || 0; // 优先使用lingli，如果没有则使用exp
    this.power = power;

    // 扩展角色属性
    this.attributes = {
      // 基础属性（有初始值）
      hp: hp,            // 生命
      attack: attack,         // 攻击
      defense: defense,         // 防御
      speed: speed,          // 速度
      critRate: critRate,     // 暴击概率，初始5%
      critDamage: critDamage,    // 暴击伤害倍率，初始1.5倍

      // 进阶属性（初始值为0）
      daoRule: daoRule,         // 大道法则
      penetration: penetration,     // 破防
      attackSpeed: 0,     // 攻击速度，默认0%
      hpBonus: 0,         // 生命加成
      defenseBonus: 0,    // 防御加成
      attackBonus: 0,     // 攻击加成
      hitRate: 0,         // 命中概率
      dodgeRate: 0,       // 闪避概率
      healEffect: 0,      // 治疗效果
      damageBonus: 0,     // 最终伤害加成
      damageReduction: 0, // 最终伤害减免
      skillDamageBonus: 0,// 技能伤害加成
      skillDamageReduction: 0, // 技能伤害减免

      // 合并用户提供的属性
      ...attributes
    };

    this.equipments = equipments;
    this.equipment = equipment; // 设置新的装备对象结构
    this.skills = skills || []; // 拥有的功法列表
    this.equippedSkills = []; // 当前装备的功法列表，支持多功法
    this.equippedSkill = equippedSkill; // 兼容旧版本的单一功法
    this.maxEquippedSkills = 3; // 最多可装备的功法数量

    // 新的技能装备系统（6个槽位）
    this.skillSlots = {
      normalAttack: null,    // 普通攻击技能（槽位0）
      activeSkill1: null,    // 主动技能1（槽位1）
      activeSkill2: null,    // 主动技能2（槽位2）
      activeSkill3: null,    // 主动技能3（槽位3）
      activeSkill4: null,    // 主动技能4（槽位4）
      activeSkill5: null     // 主动技能5（槽位5）
    };
    this.cultivation = cultivation;
    this.star = star;

    // 额外的计算属性
    this.maxHp = hp;
    this.currentHp = hp;

    // 战斗相关状态
    this.buffs = [];
    this.debuffs = [];
    this.isAlive = true;
  }

  // 获取角色属性（包含装备和功法加成）
  getAttributes() {
    // 创建一个新对象，防止修改原始属性
    let attributes = JSON.parse(JSON.stringify(this.attributes));

    // 处理老的equipments数组格式 (兼容性处理)
    if (this.equipments && Array.isArray(this.equipments)) {
      this.equipments.forEach(equip => {
        if (equip && equip.attributes) {
          for (let attr in equip.attributes) {
            if (attributes.hasOwnProperty(attr)) {
              attributes[attr] += equip.attributes[attr] || 0;
            }
          }
        }
      });
    }

    // 处理新的equipment对象格式 (按槽位)
    if (this.equipment && typeof this.equipment === 'object') {
      for (let slot in this.equipment) {
        const equip = this.equipment[slot];
        if (equip && equip.attributes) {
          for (let attr in equip.attributes) {
            if (attributes.hasOwnProperty(attr)) {
              attributes[attr] += equip.attributes[attr] || 0;
            }
          }
        }
      }
    }

    // 处理功法加成 - 从玩家的功法等级计算
    try {
      const player = AppContext.game.gameStateManager.getPlayer();
      if (player && player.skills) {
        // 导入功法配置
        const SkillConfig = require('../config/SkillConfig');

        for (const skillId in player.skills) {
          const playerSkill = player.skills[skillId];
          const skillLevel = playerSkill.level || 0;

          if (skillLevel > 0) {
            // 获取功法的所有等级加成
            for (let level = 1; level <= skillLevel; level++) {
              const levelData = SkillConfig.getSkillLevelData(skillId, level);
              if (levelData && levelData.attributes) {
                for (let attr in levelData.attributes) {
                  if (attributes.hasOwnProperty(attr)) {
                    attributes[attr] += levelData.attributes[attr] || 0;
                  }
                }
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('计算功法加成时出错:', error);
    }

    // 处理技能加成（保持兼容性）
    if (this.skills) {
      this.skills.forEach(skill => {
        if (skill && skill.attributes) {
          for (let attr in skill.attributes) {
            if (attributes.hasOwnProperty(attr)) {
              attributes[attr] += skill.attributes[attr] || 0;
            }
          }
        }
      });
    }

    // 确保属性不低于0（除了穿透属性）
    for (let attr in attributes) {
      if (attr !== 'penetration' && attributes[attr] < 0) {
        attributes[attr] = 0;
      }
    }

    return attributes;
  }

  // 计算角色战力
  calculatePower() {
    const attrs = this.getAttributes();

    // 保存旧战力值用于比较
    const oldPower = this.power || 0;

    // 更新战力计算公式，考虑所有属性
    let power = 0;

    // 基础属性权重
    power += attrs.hp * 0.5;
    power += attrs.attack * 2;
    power += attrs.defense * 1.5;
    power += attrs.speed * 1;
    power += attrs.critRate * 200;
    power += attrs.critDamage * 100;

    // 进阶属性权重
    power += attrs.daoRule * 5;
    power += attrs.penetration * 2;
    power += attrs.hpBonus * 50;
    power += attrs.defenseBonus * 50;
    power += attrs.attackBonus * 50;
    power += attrs.hitRate * 100;
    power += attrs.dodgeRate * 100;
    power += attrs.healEffect * 30;
    power += attrs.damageBonus * 50;
    power += attrs.damageReduction * 50;
    power += attrs.skillDamageBonus * 40;
    power += attrs.skillDamageReduction * 40;

    // 根据等级增加战力
    power += this.level * 100;

    // 根据装备数量和品质增加战力
    let equippedCount = 0;
    let qualityBonus = 0;

    // 品质倍率映射表
    const qualityMultiplier = {
      0: 1,    // 普通
      1: 1.5,  // 良好
      2: 2,    // 稀有
      3: 3,    // 史诗
      4: 5,    // 传说
      'common': 1,      // 普通
      'uncommon': 1.5,  // 良好
      'rare': 2,        // 稀有
      'epic': 3,        // 史诗
      'legendary': 5    // 传说
    };

    // 检查方式1: 装备数组 this.equipments
    if (Array.isArray(this.equipments)) {
      this.equipments.forEach(equipment => {
        if (equipment) {
          equippedCount++;

          // 数字或字符串品质都支持
          const qualityValue = typeof equipment.quality === 'number'
            ? equipment.quality
            : qualityMultiplier[equipment.quality] || 1;

          qualityBonus += 30 * (qualityValue);
        }
      });
    }

    // 检查方式2: 装备对象 this.equipment
    if (this.equipment && typeof this.equipment === 'object') {
      const equipSlots = ['weapon', 'armor', 'accessory', 'artifact'];
      equipSlots.forEach(slot => {
        const equipment = this.equipment[slot];
        if (equipment) {
          equippedCount++;

          // 数字或字符串品质都支持
          const qualityValue = typeof equipment.quality === 'number'
            ? equipment.quality
            : qualityMultiplier[equipment.quality] || 1;

          qualityBonus += 30 * (qualityValue);
        }
      });
    }

    power += equippedCount * 50 + qualityBonus;

    // 根据装备的功法增加战力
    if (this.equippedSkills && this.equippedSkills.length > 0) {
      // 累加所有装备功法的战力
      this.equippedSkills.forEach(skill => {
        if (skill) {
          power += skill.power || 0;
        }
      });

      // 如果有多个功法，计算组合加成
      if (this.equippedSkills.length > 1) {
        // 基础组合加成：每多一个功法增加10%战力
        const combinationBonus = (this.equippedSkills.length - 1) * 0.1;
        power = power * (1 + combinationBonus);

        // 检查是否有特殊组合效果
        const combinationEffect = this.getSkillCombinationEffect();
        if (combinationEffect && combinationEffect.powerBonus) {
          power += combinationEffect.powerBonus;
        }
      }
    }
    // 兼容旧版本的单一功法
    else if (this.equippedSkill) {
      power += this.equippedSkill.power || 0;
    }

    // 更新战力
    const newPower = Math.floor(power);
    this.power = newPower;

    // 如果战力增加了，触发战力增长动画
    if (newPower > oldPower && oldPower > 0) {
      // 使用事件系统触发战力增长动画
      if (typeof game !== 'undefined' && game.eventSystem) {
        game.eventSystem.emit('powerIncrease', {
          characterId: this.id,
          oldPower,
          newPower,
          increase: newPower - oldPower
        });
      }
    }

    return this.power;
  }

  /**
   * 获取角色用于战斗的最终属性
   * @returns {Object} 包含所有加成的战斗属性对象
   */
  getBattleStats() {
    // 获取基础属性（已包含装备和功法加成）
    const finalAttributes = this.getAttributes();

    // 这里可以添加其他来源的加成，例如境界加成、临时Buff等
    // 示例：添加境界加成 (假设有一个 getCultivationBonus 方法)
    // const cultivationBonus = this.getCultivationBonus();
    // if (cultivationBonus) {
    //   if (cultivationBonus.hp) finalAttributes.hp += cultivationBonus.hp;
    //   if (cultivationBonus.attack) finalAttributes.attack += cultivationBonus.attack;
    //   // ... 其他属性
    // }

    // 构建战斗属性对象
    const battleStats = {
      id: this.id,
      name: this.name,
      level: this.level,
      type: 'player', // 明确是玩家单位
      formationPosition: 0, // 默认位置，会在布阵时修改

      // 核心战斗属性
      maxHp: Math.round(finalAttributes.hp), // 使用计算后的最终HP作为maxHp
      hp: Math.round(finalAttributes.hp),    // 战斗开始时hp等于maxHp
      attack: Math.round(finalAttributes.attack),
      defense: Math.round(finalAttributes.defense),
      speed: Math.round(finalAttributes.speed),
      critRate: finalAttributes.critRate,
      critDamage: finalAttributes.critDamage,

      // 可以选择性地包含其他可能在战斗中有用的属性
      penetration: finalAttributes.penetration || 0, // 确保有默认值
      dodgeRate: finalAttributes.dodgeRate || 0,
      healEffect: finalAttributes.healEffect || 0,
      damageBonus: finalAttributes.damageBonus || 0,
      damageReduction: finalAttributes.damageReduction || 0,
      skillDamageBonus: finalAttributes.skillDamageBonus || 0,
      skillDamageReduction: finalAttributes.skillDamageReduction || 0,

      // 原始attributes副本，可能用于某些特殊计算
      attributes: { ...finalAttributes },

      // 技能信息（如果战斗需要）
      skills: this.skills ? this.skills.map(s => s ? s.id : null).filter(id => id !== null) : [], // 增加检查
      equippedSkillIds: this.equippedSkills ? this.equippedSkills.map(s => s ? s.id : null).filter(id => id !== null) : [],
      equippedSkillId: this.equippedSkill ? this.equippedSkill.id : null // 兼容旧版本
    };

    // --- 增加对关键战斗属性的最终检查 ---
    const requiredStats = ['maxHp', 'hp', 'attack', 'defense', 'speed'];
    for (const stat of requiredStats) {
        if (typeof battleStats[stat] !== 'number' || isNaN(battleStats[stat]) || battleStats[stat] <= 0) {
            console.error(`角色 ${this.name} 的最终战斗属性 ${stat} 无效 (${battleStats[stat]})，强制设为1`);
            battleStats[stat] = 1; // 设置为1以避免战斗逻辑错误
            if (stat === 'hp' && battleStats.maxHp < 1) battleStats.maxHp = 1;
            if (stat === 'maxHp' && battleStats.hp > battleStats.maxHp) battleStats.hp = battleStats.maxHp;
        }
    }
    if (typeof battleStats.critRate !== 'number' || isNaN(battleStats.critRate)) battleStats.critRate = 0.05;
    if (typeof battleStats.critDamage !== 'number' || isNaN(battleStats.critDamage)) battleStats.critDamage = 1.5;
    // --- 检查结束 ---

    console.log(`角色 ${this.name} 的战斗属性:`, battleStats);
    return battleStats;
  }

  // 获取角色拥有的所有功法
  getSkills() {
    return this.skills || [];
  }

  // 获取当前装备的功法列表
  getEquippedSkills() {
    return this.equippedSkills || [];
  }

  // 获取主要装备的功法（兼容旧版本）
  getEquippedSkill() {
    // 如果有新的功法列表，返回第一个
    if (this.equippedSkills && this.equippedSkills.length > 0) {
      return this.equippedSkills[0];
    }
    // 否则返回旧版本的单一功法
    return this.equippedSkill;
  }

  // 新的技能装备系统方法

  /**
   * 装备技能到指定槽位
   * @param {number} slotIndex 槽位索引（0-5）
   * @param {Object} skill 技能对象
   * @returns {boolean} 是否装备成功
   */
  equipSkillToSlot(slotIndex, skill) {
    const slotNames = ['normalAttack', 'activeSkill1', 'activeSkill2', 'activeSkill3', 'activeSkill4', 'activeSkill5'];

    if (slotIndex < 0 || slotIndex >= slotNames.length) {
      console.error('无效的槽位索引:', slotIndex);
      return false;
    }

    // 检查技能类型是否匹配槽位
    if (slotIndex === 0) {
      // 普通攻击槽位只能装备普通攻击技能
      if (skill.type !== 'normalAttack') {
        console.error('普通攻击槽位只能装备普通攻击技能');
        return false;
      }
    } else {
      // 主动技能槽位只能装备主动技能
      if (skill.type !== 'activeSkill') {
        console.error('主动技能槽位只能装备主动技能');
        return false;
      }
    }

    const slotName = slotNames[slotIndex];

    // 如果槽位已有技能，先卸下
    if (this.skillSlots[slotName]) {
      this.skillSlots[slotName].equipped = false;
      this.skillSlots[slotName].equippedBy = null;
    }

    // 装备新技能
    this.skillSlots[slotName] = skill;
    skill.equipped = true;
    skill.equippedBy = this.id;

    console.log(`成功将技能 ${skill.name} 装备到槽位 ${slotIndex}`);
    return true;
  }

  /**
   * 卸下指定槽位的技能
   * @param {number} slotIndex 槽位索引
   * @returns {Object|null} 被卸下的技能
   */
  unequipSkillFromSlot(slotIndex) {
    const slotNames = ['normalAttack', 'activeSkill1', 'activeSkill2', 'activeSkill3', 'activeSkill4', 'activeSkill5'];

    if (slotIndex < 0 || slotIndex >= slotNames.length) {
      console.error('无效的槽位索引:', slotIndex);
      return null;
    }

    const slotName = slotNames[slotIndex];
    const skill = this.skillSlots[slotName];

    if (skill) {
      skill.equipped = false;
      skill.equippedBy = null;
      this.skillSlots[slotName] = null;
      console.log(`成功卸下槽位 ${slotIndex} 的技能 ${skill.name}`);
      return skill;
    }

    return null;
  }

  /**
   * 获取指定槽位的技能
   * @param {number} slotIndex 槽位索引
   * @returns {Object|null} 技能对象
   */
  getSkillInSlot(slotIndex) {
    const slotNames = ['normalAttack', 'activeSkill1', 'activeSkill2', 'activeSkill3', 'activeSkill4', 'activeSkill5'];

    if (slotIndex < 0 || slotIndex >= slotNames.length) {
      return null;
    }

    return this.skillSlots[slotNames[slotIndex]];
  }

  /**
   * 获取所有装备的技能
   * @returns {Array} 装备的技能列表
   */
  getEquippedSkillSlots() {
    return [
      this.skillSlots.normalAttack,
      this.skillSlots.activeSkill1,
      this.skillSlots.activeSkill2,
      this.skillSlots.activeSkill3,
      this.skillSlots.activeSkill4,
      this.skillSlots.activeSkill5
    ];
  }

  // 添加功法
  addSkill(skill) {
    if (!skill) return false;

    // 确保skills是数组
    if (!this.skills) {
      this.skills = [];
    }

    // 检查是否已经拥有该功法
    const existingIndex = this.skills.findIndex(s => s && s.id === skill.id);
    if (existingIndex >= 0) {
      return false; // 已经拥有该功法
    }

    // 添加功法
    this.skills.push(skill);
    return true;
  }

  // 取消装备功法
  unequipSkill(skillId) {
    // 如果没有指定skillId，则卸下所有功法
    if (!skillId) {
      if (this.equippedSkills.length === 0 && !this.equippedSkill) return null;

      const previousSkills = [...this.equippedSkills];
      // 清空装备的功法
      this.equippedSkills = [];
      this.equippedSkill = null;

      // 重新计算战力
      this.calculatePower();

      return previousSkills;
    }

    // 查找指定的功法
    const index = this.equippedSkills.findIndex(s => s && s.id === skillId);
    if (index >= 0) {
      // 找到了，从列表中移除
      const previousSkill = this.equippedSkills[index];
      this.equippedSkills.splice(index, 1);

      // 如果是第一个功法，同时更新旧版本的equippedSkill
      if (index === 0 && this.equippedSkills.length > 0) {
        this.equippedSkill = this.equippedSkills[0];
      } else if (this.equippedSkills.length === 0) {
        this.equippedSkill = null;
      }

      // 重新计算战力
      this.calculatePower();

      return previousSkill;
    }

    // 兼容旧版本
    if (this.equippedSkill && this.equippedSkill.id === skillId) {
      const previousSkill = this.equippedSkill;
      this.equippedSkill = null;

      // 重新计算战力
      this.calculatePower();

      return previousSkill;
    }

    return null;
  }

  // 升级功法
  upgradeSkill(skillId, levels = 1) {
    // 查找功法
    const skill = this.skills.find(s => s && s.id === skillId);
    if (!skill) return false;

    // 升级功法
    const success = skill.upgrade(levels);

    // 如果是当前装备的功法之一，重新计算战力
    if (this.equippedSkills.some(s => s && s.id === skillId) ||
        (this.equippedSkill && this.equippedSkill.id === skillId)) {
      this.calculatePower();
    }

    return success;
  }

  // 提升功法星级
  upgradeSkillStar(skillId) {
    // 查找功法
    const skill = this.skills.find(s => s && s.id === skillId);
    if (!skill) return false;

    // 提升功法星级
    const success = skill.upgradeStar();

    // 如果是当前装备的功法之一，重新计算战力
    if (this.equippedSkills.some(s => s && s.id === skillId) ||
        (this.equippedSkill && this.equippedSkill.id === skillId)) {
      this.calculatePower();
    }

    return success;
  }

  // 将角色数据转换为JSON格式
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      level: this.level,
      exp: this.exp,
      lingli: this.exp, // 保持兼容性，将exp同步到lingli
      power: this.power,
      attributes: { ...this.attributes },
      // 保存老的装备数组
      equipments: this.equipments.map(e => e ? e.toJSON() : null),
      // 保存新的装备对象结构(按槽位)
      equipment: this.equipment ? Object.keys(this.equipment).reduce((obj, slot) => {
        obj[slot] = this.equipment[slot] ?
          (typeof this.equipment[slot].toJSON === 'function' ?
            this.equipment[slot].toJSON() : { ...this.equipment[slot] })
          : null;
        return obj;
      }, {}) : null,
      skills: (this.skills || []).map(s => {
        // 确保skill对象存在且有toJSON方法
        if (s && typeof s.toJSON === 'function') {
          return s.toJSON();
        } else if (s && typeof s === 'object') {
          // 如果是对象但没有toJSON方法，返回对象本身
          return { ...s };
        } else {
          // 如果不是有效对象，返回null
          console.warn('发现无效的skill对象:', s);
          return null;
        }
      }).filter(s => s !== null), // 过滤掉null值
      equippedSkills: (this.equippedSkills || []).map(s => {
        if (s && typeof s.toJSON === 'function') {
          return s.toJSON();
        } else if (s) {
          return { ...s };
        }
        return null;
      }).filter(s => s !== null),
      equippedSkill: this.equippedSkill && typeof this.equippedSkill.toJSON === 'function'
        ? this.equippedSkill.toJSON()
        : (this.equippedSkill ? { ...this.equippedSkill } : null),
      cultivation: this.cultivation,
      lingli: this.lingli,
      star: this.star
    };
  }

  /**
   * 获取升级所需灵力
   * @returns {number} 升级所需灵力
   */
  getLingliToNextLevel() {
    // 使用洞府系统的计算方法
    if (AppContext && AppContext.game && AppContext.game.dongfuSystem) {
      return AppContext.game.dongfuSystem.calculateRequiredLingli(this.cultivation, this.level);
    }

    // 如果没有洞府系统，使用默认计算方法
    // 练气期各层的灵力需求
    if (this.cultivation.includes('练气期')) {
      const layer = parseInt(this.cultivation.replace(/[^0-9]/g, '')) || 1;
      return 100 * Math.pow(1.5, layer - 1);
    }

    // 其他境界的灵力需求
    const requirements = {
      '筑基期': 5000,
      '金丹期': 20000,
      '元婴期': 50000,
      '化神期': 100000,
      '练虚期': 200000,
      '合体期': 500000,
      '大乘期': 1000000,
      '渡劫期': 2000000
    };

    return requirements[this.cultivation] || this.level * 100;
  }

  /**
   * 获取下一个修炼境界
   * @returns {string} 下一个修炼境界
   */
  getNextCultivation() {
    // 使用洞府系统的计算方法
    if (AppContext && AppContext.game && AppContext.game.dongfuSystem) {
      return AppContext.game.dongfuSystem.getNextCultivation(this.cultivation);
    }

    // 如果没有洞府系统，使用默认计算方法
    // 练气期细分为十二层
    if (this.cultivation.includes('练气期')) {
      const layer = parseInt(this.cultivation.replace(/[^0-9]/g, '')) || 1;
      if (layer < 12) {
        return `练气期${layer + 1}层`;
      } else {
        return '筑基期';
      }
    }

    // 其他境界的进阶
    const cultivations = [
      '筑基期', '金丹期', '元婴期', '化神期',
      '练虚期', '合体期', '大乘期', '渡劫期'
    ];

    const index = cultivations.indexOf(this.cultivation);
    if (index >= 0 && index < cultivations.length - 1) {
      return cultivations[index + 1];
    }

    // 如果已经是最高境界，返回当前境界
    return this.cultivation;
  }

  /**
   * 增加灵力/经验
   * @param {number} amount 增加的灵力/经验值
   * @param {boolean} allowBreakthrough 是否允许自动突破，默认为false
   * @returns {boolean} 是否升级了境界
   */
  addLingli(amount, allowBreakthrough = false) {
    if (!amount || amount <= 0) return false;

    // 更新经验值
    this.exp = (this.exp || 0) + amount;

    // 如果不允许自动突破，直接返回
    if (!allowBreakthrough) {
      return false;
    }

    // 以下代码只在允许自动突破时执行
    // 检查是否可以升级境界
    let cultivationChanged = false;
    while (this.exp >= this.getLingliToNextLevel()) {
      this.exp -= this.getLingliToNextLevel();

      // 获取下一个境界
      const nextCultivation = this.getNextCultivation();

      // 如果已经是最高境界，不再升级
      if (nextCultivation === this.cultivation) {
        break;
      }

      // 更新境界
      this.cultivation = nextCultivation;
      cultivationChanged = true;

      // 升级境界时增加属性
      this.attributes.hp += 50;
      this.attributes.attack += 10;
      this.attributes.defense += 8;
      this.attributes.speed += 5;
      this.attributes.critRate += 0.01; // 每境界增加1%暴击率
      this.attributes.critDamage += 0.05; // 每境界增加0.05倍暴击伤害

      // 如果是大境界突破（从练气期到筑基期等），额外增加属性
      if (!this.cultivation.includes('练气期')) {
        this.attributes.hp += 100;
        this.attributes.attack += 20;
        this.attributes.defense += 15;
        this.attributes.speed += 10;
        this.attributes.daoRule += 1; // 增加大道法则
      }
    }

    // 如果境界变化了，重新计算战力
    if (cultivationChanged) {
      this.calculatePower();
    }

    return cultivationChanged;
  }

  // 兼容方法，保持旧的API
  addExp(amount) {
    return this.addLingli(amount);
  }

  /**
   * 使用灵力丹增加灵力
   * @param {string} danId 灵力丹ID
   * @returns {boolean} 是否成功使用
   */
  useLingliDan(danId) {
    // 获取灵力丹信息
    if (!AppContext || !AppContext.game || !AppContext.game.dongfuSystem) {
      console.error('无法获取洞府系统');
      return false;
    }

    const danInfo = AppContext.game.dongfuSystem.getLingliDanInfo(danId);
    if (!danInfo) {
      console.error(`无法找到ID为${danId}的灵力丹`);
      return false;
    }

    // 增加灵力
    this.addLingli(danInfo.lingliBonus);

    return true;
  }

  // 获取升级所需经验（兼容方法）
  getExpToNextLevel() {
    return this.getLingliToNextLevel();
  }

  // 装备物品
  equip(equipment, slot) {
    console.log(`Character.equip: 给角色${this.name}装备 ${equipment ? equipment.name : '未知装备'} 到槽位 ${slot}`);

    // 支持旧的数组格式（兼容性）
    if (typeof slot === 'number') {
      // 检查槽位是否有效
      if (slot < 0 || slot >= this.equipments.length) {
        return false;
      }

      // 保存之前的装备
      const previousEquipment = this.equipments[slot];

      // 装备新物品
      this.equipments[slot] = equipment;

      // 同时更新新的equipment对象格式
      if (!this.equipment) {
        this.equipment = {};
      }

      // 映射槽位索引到类型
      const slotTypes = ['weapon', 'armor', 'accessory', 'artifact'];
      if (slot >= 0 && slot < slotTypes.length) {
        this.equipment[slotTypes[slot]] = equipment;
      }
    }
    // 支持新的对象格式，使用type字段
    else if (typeof slot === 'string') {
      // 确保equipment对象存在
      if (!this.equipment) {
        this.equipment = {};
      }

      // 保存之前的装备
      const previousEquipment = this.equipment[slot];

      // 装备新物品
      this.equipment[slot] = equipment;

      // 更新数组格式（兼容性）
      const slotIndex = ['weapon', 'armor', 'accessory', 'artifact'].indexOf(slot);
      if (slotIndex !== -1 && Array.isArray(this.equipments)) {
        this.equipments[slotIndex] = equipment;
      }
    }

    // 重新计算战力
    this.calculatePower();

    // 保存游戏状态
    try {
      if (AppContext && AppContext.game && AppContext.game.gameStateManager) {
        AppContext.game.gameStateManager.updateCharacter(this.id, this);
        console.log(`Character.equip: 更新角色${this.name}的装备数据成功`);
      }
    } catch (e) {
      console.error('装备后保存角色数据失败:', e);
    }

    return true;
  }

  // 移除装备
  unequip(slot) {
    console.log(`Character.unequip: 移除角色${this.name}槽位${slot}的装备`);

    let previousEquipment = null;

    // 支持旧的数组格式
    if (typeof slot === 'number') {
      // 检查槽位是否有效
      if (slot < 0 || slot >= this.equipments.length) {
        return null;
      }

      // 检查槽位是否有装备
      if (!this.equipments[slot]) {
        return null;
      }

      // 保存之前的装备
      previousEquipment = this.equipments[slot];

      // 移除装备
      this.equipments[slot] = null;

      // 同时更新新的equipment对象格式
      if (this.equipment) {
        const slotTypes = ['weapon', 'armor', 'accessory', 'artifact'];
        if (slot >= 0 && slot < slotTypes.length) {
          this.equipment[slotTypes[slot]] = null;
        }
      }
    }
    // 支持新的对象格式
    else if (typeof slot === 'string') {
      // 确保equipment对象存在
      if (!this.equipment) {
        return null;
      }

      // 检查槽位是否有装备
      if (!this.equipment[slot]) {
        return null;
      }

      // 保存之前的装备
      previousEquipment = this.equipment[slot];

      // 移除装备
      this.equipment[slot] = null;

      // 更新数组格式（兼容性）
      const slotIndex = ['weapon', 'armor', 'accessory', 'artifact'].indexOf(slot);
      if (slotIndex !== -1 && Array.isArray(this.equipments)) {
        this.equipments[slotIndex] = null;
      }
    }

    // 重新计算战力
    this.calculatePower();

    // 保存游戏状态
    try {
      if (AppContext && AppContext.game && AppContext.game.gameStateManager) {
        AppContext.game.gameStateManager.updateCharacter(this.id, this);
        console.log(`Character.unequip: 更新角色${this.name}的装备数据成功`);
      }
    } catch (e) {
      console.error('卸下装备后保存角色数据失败:', e);
    }

    return previousEquipment;
  }

  // 检查角色是否可以突破境界
  canAdvanceCultivation() {
    // 获取当前境界等级和下一境界等级
    const currentLevel = this.level;
    const nextRealmLevel = this.getNextRealmLevel();

    // 如果已经是最高境界，返回false
    if (nextRealmLevel === currentLevel) {
      return false;
    }

    // 检查是否可以突破到下一个境界(每3级为一个大境界)
    const isBreakthrough = Math.floor((nextRealmLevel - 1) / 3) > Math.floor((currentLevel - 1) / 3);

    // 如果不是突破到新境界，只是阶段提升，直接返回true
    if (!isBreakthrough) {
      return true;
    }

    // 突破境界需要满足条件：累积足够的修炼经验，这里简化为等级足够高
    // 如果需要加入更多条件，可以在这里添加
    return this.exp >= this.getExpToNextLevel() * 3; // 需要积累3倍于普通升级的经验
  }

  // 突破境界
  advanceCultivation() {
    if (!this.canAdvanceCultivation()) {
      return false;
    }

    // 获取当前境界等级和下一境界等级
    const currentLevel = this.level;
    const nextRealmLevel = this.getNextRealmLevel();

    // 检查是否是境界突破(每3级为一个大境界)
    const isBreakthrough = Math.floor((nextRealmLevel - 1) / 3) > Math.floor((currentLevel - 1) / 3);

    // 保存原始境界名称用于显示
    const oldRealm = this.getCultivationRealm();

    // 提升等级
    this.level = nextRealmLevel;

    // 突破到新境界时获得更多属性提升
    if (isBreakthrough) {
      this.exp = 0; // 突破后经验清零

      // 大境界突破时属性大幅提升
      this.attributes.hp += 100;
      this.attributes.attack += 20;
      this.attributes.defense += 15;
      this.attributes.speed += 10;
      this.attributes.critRate += 0.02; // 增加2%暴击率
      this.attributes.critDamage += 0.1; // 增加0.1倍暴击伤害
      this.attributes.daoRule += 1; // 增加大道法则
    } else {
      // 小境界提升时属性小幅提升
      this.attributes.hp += 50;
      this.attributes.attack += 10;
      this.attributes.defense += 7;
      this.attributes.speed += 5;
      this.attributes.critRate += 0.01; // 增加1%暴击率
      this.attributes.critDamage += 0.05; // 增加0.05倍暴击伤害
    }

    // 更新战力
    this.calculatePower();

    // 返回突破结果对象
    return {
      success: true,
      isBreakthrough: isBreakthrough,
      oldRealm: oldRealm,
      newRealm: this.getCultivationRealm()
    };
  }

  /**
   * 获取功法组合效果
   * 检查当前装备的功法是否形成特殊组合
   * @returns {Object|null} 组合效果对象或null
   */
  getSkillCombinationEffect() {
    if (!this.equippedSkills || this.equippedSkills.length < 2) return null;

    // 获取所有装备功法的ID
    const skillIds = this.equippedSkills.map(s => s.id).sort();

    // 定义功法组合效果
    const combinationEffects = {
      // 五行相生组合
      'five_elements_generation': {
        skills: ['metal_skill', 'water_skill', 'wood_skill', 'fire_skill', 'earth_skill'],
        name: '五行相生',
        description: '金生水，水生木，木生火，火生土，土生金的五行相生组合',
        powerBonus: 500,
        attributeBonus: {
          hp: 200,
          attack: 50,
          defense: 50,
          speed: 20
        }
      },
      // 阴阳平衡组合
      'yin_yang_balance': {
        skills: ['yin_skill', 'yang_skill'],
        name: '阴阳平衡',
        description: '阴阳功法相互配合，达到平衡状态',
        powerBonus: 300,
        attributeBonus: {
          critRate: 0.1,
          critDamage: 0.3
        }
      },
      // 基础组合：基础练气决 + 金阳神功
      'basic_golden': {
        skills: ['basic_qi_skill', 'golden_sun_skill'],
        name: '基础金阳',
        description: '基础练气决与金阳神功的组合，增强防御能力',
        powerBonus: 200,
        attributeBonus: {
          defense: 30,
          defenseBonus: 10
        }
      }
    };

    // 检查是否匹配任何组合
    for (const key in combinationEffects) {
      const effect = combinationEffects[key];
      // 检查是否包含组合中的所有功法
      const hasAllSkills = effect.skills.every(id => skillIds.includes(id));
      if (hasAllSkills) {
        return effect;
      }
    }

    return null;
  }

  // 装备功法
  equipSkill(skillId) {
    console.log(`尝试装备功法, id: ${skillId}, 角色: ${this.name}`);

    // 处理技能ID为对象的情况
    if (typeof skillId === 'object' && skillId !== null) {
      if (skillId.id) {
        console.log(`skillId是对象，使用其id属性: ${skillId.id}`);
        skillId = skillId.id;
      } else {
        console.error('无效的技能对象，缺少id属性');
        return false;
      }
    }

    // 查找功法
    let skillToEquip = null;

    // 查找当前角色拥有的功法
    if (this.skills && Array.isArray(this.skills)) {
      for (let i = 0; i < this.skills.length; i++) {
        const skill = this.skills[i];
        if (skill && skill.id === skillId) {
          skillToEquip = skill;
          console.log(`在角色功法列表中找到功法: ${skill.name || skill.id}`);
          break;
        }
      }
    } else {
      console.warn(`角色 ${this.name} 的 skills 属性无效:`, this.skills);
      this.skills = []; // 初始化为空数组
    }

    // 如果在角色技能列表中没找到，尝试从全局管理器获取
    if (!skillToEquip) {
      console.log(`角色${this.name}没有ID为${skillId}的功法，尝试从功法管理器获取`);

      // 直接使用导入的AppContext
      try {
        const skillManager = AppContext.game.skillManager;
        if (!skillManager) {
          console.error('功法管理器不存在');
          return false;
        }

        const templateSkill = skillManager.getSkillById(skillId);

        if (templateSkill) {
          console.log(`从功法管理器找到功法模板: ${templateSkill.name}`);

          const newSkill = skillManager.createSkill(skillId);
          if (newSkill) {
            // 添加到角色功法列表
            this.addSkill(newSkill);
            skillToEquip = newSkill;
            console.log(`成功创建并添加功法: ${newSkill.name}`);
          } else {
            console.error(`无法创建功法实例: ${skillId}`);
            return false;
          }
        } else {
          console.error(`功法管理器中也找不到ID为${skillId}的功法`);
          return false;
        }
      } catch (err) {
        console.error('访问功法管理器时出错:', err);
        return false;
      }
    }

    // 如果该功法已经装备，不需要重复装备
    if (this.equippedSkills.some(s => s && s.id === skillToEquip.id)) {
      console.log(`功法${skillToEquip.name || skillToEquip.id}已经装备`);
      return true;
    }

    // 检查是否满足装备条件（如果有canEquip方法）
    if (skillToEquip.canEquip && typeof skillToEquip.canEquip === 'function') {
      if (!skillToEquip.canEquip(this)) {
        console.log(`角色不满足装备条件，无法装备功法${skillToEquip.name || skillToEquip.id}`);
        return false;
      }
    }

    // 检查是否已达到最大装备数量
    if (this.equippedSkills.length >= this.maxEquippedSkills) {
      console.log(`已达到最大装备功法数量(${this.maxEquippedSkills})，需要先卸下一个功法`);
      return false;
    }

    // 装备新功法
    if (skillToEquip.equip && typeof skillToEquip.equip === 'function') {
      skillToEquip.equip(this);
    }

    // 添加到装备功法列表
    this.equippedSkills.push(skillToEquip);

    // 同时更新旧版本的单一功法引用（如果是第一个功法）
    if (this.equippedSkills.length === 1) {
      this.equippedSkill = skillToEquip;
    }

    // 重新计算战力
    this.calculatePower();

    console.log(`成功装备功法：${skillToEquip.name || skillToEquip.id}`);
    return true;
  }

  /**
   * 获取角色的星级显示
   */
  getStarDisplay() {
    return '★'.repeat(this.star);
  }



  /**
   * 升级角色等级
   */
  levelUp() {
    this.level += 1;
    this.updateAttributes();
  }

  /**
   * 更新角色属性（基于等级和星级）
   */
  updateAttributes() {
    // 获取基础属性倍率 - 使用默认品质
    const baseAttributes = generateCharacterAttributes(0, this.level, this.star);

    // 更新属性
    this.attributes.hp = baseAttributes.hp;
    this.attributes.attack = baseAttributes.attack;
    this.attributes.defense = baseAttributes.defense;
    this.attributes.speed = baseAttributes.speed;
    this.attributes.critRate = baseAttributes.critRate;
    this.attributes.critDamage = baseAttributes.critDamage;
    this.attributes.daoRule = baseAttributes.daoRule;
    this.attributes.penetration = baseAttributes.penetration;

    // 更新最大生命值
    this.maxHp = this.attributes.hp;
    this.currentHp = this.attributes.hp;
  }

  /**
   * 计算战斗中的实际属性（考虑buff和debuff）
   */
  getEffectiveAttributes() {
    // 复制基础属性
    const effectiveAttributes = {
      hp: this.currentHp,
      attack: this.attributes.attack,
      defense: this.attributes.defense,
      speed: this.attributes.speed,
      critRate: this.attributes.critRate,
      critDamage: this.attributes.critDamage,
      daoRule: this.attributes.daoRule,
      penetration: this.attributes.penetration
    };

    // 应用buff
    for (const buff of this.buffs) {
      if (buff.type === 'attribute' && buff.isActive) {
        const attribute = buff.attribute;
        const value = buff.value;

        if (attribute in effectiveAttributes) {
          if (buff.isPercentage) {
            // 百分比加成
            effectiveAttributes[attribute] *= (1 + value / 100);
          } else {
            // 固定值加成
            effectiveAttributes[attribute] += value;
          }
        }
      }
    }

    // 应用debuff
    for (const debuff of this.debuffs) {
      if (debuff.type === 'attribute' && debuff.isActive) {
        const attribute = debuff.attribute;
        const value = debuff.value;

        if (attribute in effectiveAttributes) {
          if (debuff.isPercentage) {
            // 百分比减益
            effectiveAttributes[attribute] *= (1 - value / 100);
          } else {
            // 固定值减益
            effectiveAttributes[attribute] -= value;
          }
        }
      }
    }

    // 确保数值不会出现负数
    for (const key in effectiveAttributes) {
      if (key !== 'penetration') { // 穿透可以为负数，表示减伤
        effectiveAttributes[key] = Math.max(0, effectiveAttributes[key]);
      }
    }

    // 暴击率最高100%
    effectiveAttributes.critRate = Math.min(100, effectiveAttributes.critRate);

    return effectiveAttributes;
  }

  /**
   * 处理受到伤害
   */
  takeDamage(amount) {
    this.currentHp -= amount;
    if (this.currentHp <= 0) {
      this.currentHp = 0;
      this.isAlive = false;
    }
  }

  /**
   * 治疗生命值
   */
  heal(amount) {
    if (!this.isAlive) return;

    this.currentHp += amount;
    if (this.currentHp > this.maxHp) {
      this.currentHp = this.maxHp;
    }
  }

  /**
   * 复活角色
   */
  revive(healthPercentage = 30) {
    if (!this.isAlive) {
      this.isAlive = true;
      this.currentHp = Math.floor(this.maxHp * healthPercentage / 100);
    }
  }

  /**
   * 添加buff
   */
  addBuff(buff) {
    this.buffs.push(buff);
  }

  /**
   * 添加debuff
   */
  addDebuff(debuff) {
    this.debuffs.push(debuff);
  }

  /**
   * 更新buff和debuff的持续时间
   */
  updateBuffsAndDebuffs() {
    // 更新buff
    for (let i = this.buffs.length - 1; i >= 0; i--) {
      const buff = this.buffs[i];
      buff.duration--;

      if (buff.duration <= 0) {
        this.buffs.splice(i, 1);
      }
    }

    // 更新debuff
    for (let i = this.debuffs.length - 1; i >= 0; i--) {
      const debuff = this.debuffs[i];
      debuff.duration--;

      if (debuff.duration <= 0) {
        this.debuffs.splice(i, 1);
      }
    }
  }

  /**
   * 重置战斗状态
   */
  resetBattleState() {
    this.currentHp = this.maxHp;
    this.isAlive = true;
    this.buffs = [];
    this.debuffs = [];
  }

  /**
   * 获取角色修炼境界名称
   * @returns {string} 境界名称，如"练气初期"
   */
  getCultivationRealm() {
    // 定义9大境界，每个境界3个阶段
    const realms = [
      '练气', '筑基', '金丹', '元婴',
      '化神', '返虚', '合道', '渡劫', '大乘'
    ];

    const stages = ['初期', '中期', '后期'];

    // 计算境界索引和阶段索引
    const realmIndex = Math.floor((this.level - 1) / 3);
    const stageIndex = (this.level - 1) % 3;

    // 确保索引不越界
    const safeRealmIndex = Math.min(realmIndex, realms.length - 1);

    // 组合境界名称
    return `${realms[safeRealmIndex]}${stages[stageIndex]}`;
  }

  /**
   * 获取角色等级显示文本
   * 用修炼境界替代原来的数字等级
   * @returns {string} 境界名称
   */
  getLevelText() {
    return this.getCultivationRealm();
  }

  /**
   * 获取下一境界所需等级
   * @returns {number} 下一境界等级
   */
  getNextRealmLevel() {
    // 计算当前所处的境界和阶段
    const realmIndex = Math.floor((this.level - 1) / 3);
    const stageIndex = (this.level - 1) % 3;

    // 如果已经是最高境界的最高阶段，返回当前等级
    if (realmIndex >= 8 && stageIndex >= 2) {
      return this.level;
    }

    // 否则返回下一个境界等级
    return this.level + 1;
  }

  /**
   * 获取当前境界的下一阶段名称
   * @returns {string} 下一境界名称
   */
  getNextRealmText() {
    // 获取下一境界等级
    const nextLevel = this.getNextRealmLevel();

    // 如果等级没变，说明已经是最高境界
    if (nextLevel === this.level) {
      return "已达最高境界";
    }

    // 临时设置等级
    const originalLevel = this.level;
    this.level = nextLevel;

    // 获取境界名称
    const nextRealm = this.getCultivationRealm();

    // 恢复原等级
    this.level = originalLevel;

    return nextRealm;
  }
}

export default Character;

